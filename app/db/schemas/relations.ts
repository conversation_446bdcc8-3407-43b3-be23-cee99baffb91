import { relations } from "drizzle-orm";

import { accounts } from "./account";
import { accountGroups } from "./account_group";
import { categories } from "./category";
import { users } from "./user";

export const accountsRelations = relations(accounts, ({ one }) => ({
  user: one(users, { fields: [accounts.userId], references: [users.id] }),
  group: one(accountGroups, { fields: [accounts.groupId], references: [accountGroups.id] }),
}));

export const accountGroupsRelations = relations(accountGroups, ({ one }) => ({
  user: one(users, { fields: [accountGroups.userId], references: [users.id] }),
}));

export const categoriesRelations = relations(categories, ({ one }) => ({
  user: one(users, { fields: [categories.userId], references: [users.id] }),
}));

export const usersRelations = relations(users, ({ many }) => ({
  accounts: many(accounts),
  accountGroups: many(accountGroups),
  categories: many(categories),
}));
