import type { AppBindings } from "~/types";
import type { Events } from "./types";

import { defineHandlers } from "@hono/event-emitter";

import * as accounts from "~/features/accounts/event-handlers";

import { TransactionCreatedEvent, TransactionDeletedEvent, TransactionUpdatedEvent } from "./types";

const handlers = defineHandlers<Events, AppBindings>({
  [TransactionCreatedEvent]: [accounts.transactionCreated],
  [TransactionUpdatedEvent]: [accounts.transactionUpdated],
  [TransactionDeletedEvent]: [accounts.transactionDeleted],
});

export default handlers;
