import type { OpenAPIHono } from "@hono/zod-openapi";
import type { AppBindings } from "~/types";

import accounts from "./accounts/router";
import auth from "./auth/router";
import categories from "./categories/router";
import transactions from "./transactions/router";
import users from "./users/router";

export const addApiV1Routes = (app: OpenAPIHono<AppBindings>) => {
  app.route("/api/v1", auth);
  app.route("/api/v1", users);
  app.route("/api/v1", accounts);
  app.route("/api/v1", categories);
  app.route("/api/v1", transactions);
};
