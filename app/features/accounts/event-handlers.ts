import type { Events, TransactionCreatedEvent, TransactionDeletedEvent, TransactionUpdatedEvent } from "~/events/types";
import type { AppBindings } from "~/types";

import { defineHandler } from "@hono/event-emitter";

export const transactionCreated = defineHandler<Events, typeof TransactionCreatedEvent, AppBindings>((c, payload) => {
  const user = c.get("user");
  console.log("Transaction created", user, payload);
});

export const transactionUpdated = defineHandler<Events, typeof TransactionUpdatedEvent, AppBindings>((c, payload) => {
  const user = c.get("user");
  console.log("Transaction updated", user, payload);
});

export const transactionDeleted = defineHandler<Events, typeof TransactionDeletedEvent, AppBindings>((c, payload) => {
  const user = c.get("user");
  console.log("Transaction deleted", user, payload);
});
