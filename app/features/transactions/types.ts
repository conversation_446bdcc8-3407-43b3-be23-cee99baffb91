import type { transactionSelectSchema } from "~/db/schemas";
import type { transactionCreateSchema, transactionResponseSchema, transactionUpdateSchema } from "./schemas";

import { z } from "@hono/zod-openapi";
import { z as z4 } from "zod/v4";

// Transaction Types
export type CreateTransactionData = z.infer<typeof transactionCreateSchema>;
export type UpdateTransactionData = z.infer<typeof transactionUpdateSchema>;
export type Transaction = z4.infer<typeof transactionSelectSchema>;
export type TransactionResponse = z.infer<typeof transactionResponseSchema>;
