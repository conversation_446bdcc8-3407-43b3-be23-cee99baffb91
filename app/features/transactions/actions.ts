import type { User } from "~/features/users/types";
import type { CreateTransactionData, Transaction, UpdateTransactionData } from "./types";

import { and, eq } from "drizzle-orm";
import { HTTPException } from "hono/http-exception";

import db from "~/db";
import { accounts, transactions } from "~/db/schemas";
import { convertAmount } from "~/features/currencies/actions";
import { StatusCodes } from "~/lib/status-codes";

export const getTransactionsByUserId = async (userId: string): Promise<Transaction[]> => {
  return await db.select().from(transactions).where(eq(transactions.userId, userId));
};

export const getTransactionById = async (id: string, userId: string): Promise<Transaction | null> => {
  const [transaction] = await db
    .select()
    .from(transactions)
    .where(and(eq(transactions.id, id), eq(transactions.userId, userId)));

  return transaction || null;
};

export const createTransaction = async (user: User, data: CreateTransactionData): Promise<Transaction> => {
  // Validate that the account belongs to the user
  const [account] = await db
    .select()
    .from(accounts)
    .where(and(eq(accounts.id, data.accountId), eq(accounts.userId, user.id)));

  if (!account) {
    throw new HTTPException(StatusCodes.BAD_REQUEST, { message: "Account not found" });
  }

  // For transfer transactions, validate the destination account
  let accountTo = null;
  if (data.type === "transfer") {
    if (!data.accountToId || !data.amountTo) {
      throw new HTTPException(StatusCodes.BAD_REQUEST, {
        message: "For transfer transactions, accountToId and amountTo are required",
      });
    }

    const [foundAccountTo] = await db
      .select()
      .from(accounts)
      .where(and(eq(accounts.id, data.accountToId), eq(accounts.userId, user.id)));

    if (!foundAccountTo) {
      throw new HTTPException(StatusCodes.BAD_REQUEST, { message: "Destination account not found" });
    }

    accountTo = foundAccountTo;
  } else {
    // For non-transfer transactions, ensure accountToId and amountTo are not provided
    if (data.accountToId || data.amountTo) {
      throw new HTTPException(StatusCodes.BAD_REQUEST, {
        message: "For non-transfer transactions, accountToId and amountTo should not be provided",
      });
    }
  }

  // Convert amounts to base currency
  const baseAmount = await convertAmount(data.amount, account.currency, user.baseCurrency, data.transactionDate);

  let baseAmountTo = null;
  if (data.type === "transfer" && accountTo && data.amountTo) {
    baseAmountTo = await convertAmount(data.amountTo, accountTo.currency, user.baseCurrency, data.transactionDate);
  }

  const [transaction] = await db
    .insert(transactions)
    .values({
      userId: user.id,
      transactionDate: data.transactionDate,
      categoryId: data.categoryId,
      type: data.type,
      description: data.description,
      accountId: data.accountId,
      amount: data.amount,
      accountToId: data.accountToId,
      amountTo: data.amountTo,
      baseAmount,
      baseAmountTo,
    })
    .returning();

  if (!transaction) {
    throw new HTTPException(StatusCodes.BAD_REQUEST, { message: "Failed to create transaction" });
  }

  return transaction;
};

export const updateTransaction = async (
  id: string,
  user: User,
  data: UpdateTransactionData
): Promise<Transaction> => {
  // Check if transaction exists and belongs to user
  const existingTransaction = await getTransactionById(id, user.id);
  if (!existingTransaction) {
    throw new HTTPException(StatusCodes.NOT_FOUND, { message: "Transaction not found" });
  }

  // Validate account if provided
  let account = null;
  if (data.accountId) {
    const [foundAccount] = await db
      .select()
      .from(accounts)
      .where(and(eq(accounts.id, data.accountId), eq(accounts.userId, user.id)));

    if (!foundAccount) {
      throw new HTTPException(StatusCodes.BAD_REQUEST, { message: "Account not found" });
    }
    account = foundAccount;
  }

  // For transfer transactions, validate the destination account
  let accountTo = null;
  if (data.type === "transfer" || (existingTransaction.type === "transfer" && !data.type)) {
    if (!data.accountToId && !existingTransaction.accountToId) {
      throw new HTTPException(StatusCodes.BAD_REQUEST, {
        message: "For transfer transactions, accountToId is required",
      });
    }

    const accountToIdToCheck = data.accountToId || existingTransaction.accountToId;
    if (accountToIdToCheck) {
      const [foundAccountTo] = await db
        .select()
        .from(accounts)
        .where(and(eq(accounts.id, accountToIdToCheck), eq(accounts.userId, user.id)));

      if (!foundAccountTo) {
        throw new HTTPException(StatusCodes.BAD_REQUEST, { message: "Destination account not found" });
      }
      accountTo = foundAccountTo;
    }
  }

  // Prepare update data
  const updateData: Partial<Transaction> = {};

  // Copy provided fields
  if (data.transactionDate !== undefined) updateData.transactionDate = data.transactionDate;
  if (data.categoryId !== undefined) updateData.categoryId = data.categoryId;
  if (data.type !== undefined) updateData.type = data.type;
  if (data.description !== undefined) updateData.description = data.description;
  if (data.accountId !== undefined) updateData.accountId = data.accountId;
  if (data.amount !== undefined) updateData.amount = data.amount;
  if (data.accountToId !== undefined) updateData.accountToId = data.accountToId;
  if (data.amountTo !== undefined) updateData.amountTo = data.amountTo;

  // Recalculate base amounts if necessary
  const finalAccount = account || (await db.select().from(accounts).where(eq(accounts.id, data.accountId || existingTransaction.accountId)))[0];
  const finalTransactionDate = data.transactionDate || existingTransaction.transactionDate;
  const finalAmount = data.amount || existingTransaction.amount;

  if (data.amount !== undefined || data.accountId !== undefined || data.transactionDate !== undefined) {
    updateData.baseAmount = await convertAmount(finalAmount, finalAccount.currency, user.baseCurrency, finalTransactionDate);
  }

  const finalType = data.type || existingTransaction.type;
  if (finalType === "transfer") {
    const finalAccountTo = accountTo || (await db.select().from(accounts).where(eq(accounts.id, data.accountToId || existingTransaction.accountToId!)))[0];
    const finalAmountTo = data.amountTo || existingTransaction.amountTo;

    if (finalAmountTo && (data.amountTo !== undefined || data.accountToId !== undefined || data.transactionDate !== undefined)) {
      updateData.baseAmountTo = await convertAmount(finalAmountTo, finalAccountTo.currency, user.baseCurrency, finalTransactionDate);
    }
  } else {
    // For non-transfer transactions, clear transfer-related fields
    updateData.accountToId = null;
    updateData.amountTo = null;
    updateData.baseAmountTo = null;
  }

  const [updatedTransaction] = await db
    .update(transactions)
    .set(updateData)
    .where(and(eq(transactions.id, id), eq(transactions.userId, user.id)))
    .returning();

  if (!updatedTransaction) {
    throw new HTTPException(StatusCodes.BAD_REQUEST, { message: "Failed to update transaction" });
  }

  return updatedTransaction;
};

export const deleteTransaction = async (id: string, userId: string): Promise<void> => {
  const result = await db
    .delete(transactions)
    .where(and(eq(transactions.id, id), eq(transactions.userId, userId)));

  if (result.rowCount === 0) {
    throw new HTTPException(StatusCodes.NOT_FOUND, { message: "Transaction not found" });
  }
};
