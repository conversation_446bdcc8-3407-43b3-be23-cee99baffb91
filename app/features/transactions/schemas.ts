import { z } from "@hono/zod-openapi";

import { decimal, transactionType } from "~/common/schemas";

export const transactionCreateSchema = z
  .object({
    transactionDate: z.string().date().openapi({ example: "2024-01-15" }),
    categoryId: z.string().uuid().nullish().openapi({ example: "123e4567-e89b-12d3-a456-************" }),
    type: transactionType("Transaction type"),
    description: z.string().nullish().openapi({ example: "Grocery shopping at Walmart" }),
    accountId: z.string().uuid().openapi({ example: "123e4567-e89b-12d3-a456-************" }),
    amount: decimal(),
    accountToId: z.string().uuid().nullish().openapi({ example: "123e4567-e89b-12d3-a456-************" }),
    amountTo: decimal().nullish(),
  })
  .openapi("TransactionCreateRequest")
  .refine(
    (data) => {
      // For transfer transactions, accountToId and amountTo are required
      if (data.type === "transfer") {
        return (
          data.accountToId !== null &&
          data.accountToId !== undefined &&
          data.amountTo !== null &&
          data.amountTo !== undefined
        );
      }
      return true;
    },
    {
      message: "For transfer transactions, accountToId and amountTo are required",
      path: ["accountToId"],
    }
  )
  .refine(
    (data) => {
      // For non-transfer transactions, accountToId and amountTo should be null
      if (data.type !== "transfer") {
        return (
          (data.accountToId === null || data.accountToId === undefined) &&
          (data.amountTo === null || data.amountTo === undefined)
        );
      }
      return true;
    },
    {
      message: "For non-transfer transactions, accountToId and amountTo should not be provided",
      path: ["accountToId"],
    }
  );

export const transactionUpdateSchema = z
  .object({
    transactionDate: z.string().date().optional().openapi({ example: "2024-01-15" }),
    categoryId: z.string().uuid().nullish().openapi({ example: "123e4567-e89b-12d3-a456-************" }),
    type: transactionType("Transaction type").optional(),
    description: z.string().nullish().openapi({ example: "Grocery shopping at Walmart" }),
    accountId: z.string().uuid().optional().openapi({ example: "123e4567-e89b-12d3-a456-************" }),
    amount: decimal().optional(),
    accountToId: z.string().uuid().nullish().openapi({ example: "123e4567-e89b-12d3-a456-************" }),
    amountTo: decimal().nullish(),
  })
  .openapi("TransactionUpdateRequest")
  .refine(
    (data) => {
      // For transfer transactions, accountToId and amountTo are required
      if (data.type === "transfer") {
        return (
          data.accountToId !== null &&
          data.accountToId !== undefined &&
          data.amountTo !== null &&
          data.amountTo !== undefined
        );
      }
      return true;
    },
    {
      message: "For transfer transactions, accountToId and amountTo are required",
      path: ["accountToId"],
    }
  )
  .refine(
    (data) => {
      // For non-transfer transactions, accountToId and amountTo should be null
      if (data.type && data.type !== "transfer") {
        return (
          (data.accountToId === null || data.accountToId === undefined) &&
          (data.amountTo === null || data.amountTo === undefined)
        );
      }
      return true;
    },
    {
      message: "For non-transfer transactions, accountToId and amountTo should not be provided",
      path: ["accountToId"],
    }
  );

export const transactionResponseSchema = z
  .object({
    id: z.string().uuid().openapi({ example: "123e4567-e89b-12d3-a456-************" }),
    transactionDate: z.string().date().openapi({ example: "2024-01-15" }),
    categoryId: z.string().uuid().nullable().openapi({ example: "123e4567-e89b-12d3-a456-************" }),
    type: transactionType("Transaction type"),
    description: z.string().nullable().openapi({ example: "Grocery shopping at Walmart" }),
    accountId: z.string().uuid().openapi({ example: "123e4567-e89b-12d3-a456-************" }),
    amount: decimal(),
    accountToId: z.string().uuid().nullable().openapi({ example: "123e4567-e89b-12d3-a456-************" }),
    amountTo: decimal().nullable(),
    baseAmount: decimal(),
    baseAmountTo: decimal().nullable(),
    createdAt: z.string().datetime().openapi({ example: "2024-01-15T10:30:00Z" }),
    updatedAt: z.string().datetime().openapi({ example: "2024-01-15T10:30:00Z" }),
  })
  .openapi("TransactionResponse");

export const transactionListQuerySchema = z
  .object({
    page: z.coerce.number().int().min(1).default(1).openapi({ example: 1 }),
    limit: z.coerce.number().int().min(1).max(100).default(20).openapi({ example: 20 }),
  })
  .openapi("TransactionListQuery");

export const transactionListMetaSchema = z
  .object({
    total: z.number().int().min(0).openapi({ example: 150 }),
    page: z.number().int().min(1).openapi({ example: 1 }),
    limit: z.number().int().min(1).openapi({ example: 20 }),
    count: z.number().int().min(0).openapi({ example: 20 }),
  })
  .openapi("TransactionListMeta");

export const transactionListResponseSchema = z
  .object({
    items: z.array(transactionResponseSchema),
    meta: transactionListMetaSchema,
  })
  .openapi("TransactionListResponse");
