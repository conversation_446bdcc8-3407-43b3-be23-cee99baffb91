import type { App<PERSON>out<PERSON><PERSON><PERSON><PERSON> } from "~/types";
import type {
  CreateTransactionRoute,
  DeleteTransactionRoute,
  GetTransactionRoute,
  ListTransactionsRoute,
  UpdateTransactionRoute,
} from "./routes";

import { StatusCodes } from "~/lib/status-codes";

import {
  createTransaction as createTransactionAction,
  deleteTransaction as deleteTransactionAction,
  getTransactionById,
  getTransactionsByUserId,
  updateTransaction as updateTransactionAction,
} from "./actions";
import { mapTransactionResponse } from "./mappers";

export const listTransactions: AppRouteHandler<ListTransactionsRoute> = async (c) => {
  const user = c.get("user");
  const query = c.req.valid("query");

  const { items, total } = await getTransactionsByUserId(user.id, query);

  const response = {
    items: items.map(mapTransactionResponse),
    meta: {
      total,
      page: query.page,
      limit: query.limit,
      count: items.length,
    },
  };

  return c.json(response, StatusCodes.OK);
};

export const createTransaction: AppRouteHandler<CreateTransactionRoute> = async (c) => {
  const user = c.get("user");
  const data = c.req.valid("json");

  const transaction = await createTransactionAction(user, data);
  return c.json(mapTransactionResponse(transaction), StatusCodes.CREATED);
};

export const getTransaction: AppRouteHandler<GetTransactionRoute> = async (c) => {
  const user = c.get("user");
  const { id } = c.req.valid("param");

  const transaction = await getTransactionById(id, user.id);
  if (!transaction) {
    return c.json({ success: false, message: "Transaction not found" }, StatusCodes.NOT_FOUND);
  }

  return c.json(mapTransactionResponse(transaction), StatusCodes.OK);
};

export const updateTransaction: AppRouteHandler<UpdateTransactionRoute> = async (c) => {
  const user = c.get("user");
  const { id } = c.req.valid("param");
  const data = c.req.valid("json");

  const transaction = await updateTransactionAction(id, user, data);
  return c.json(mapTransactionResponse(transaction), StatusCodes.OK);
};

export const deleteTransaction: AppRouteHandler<DeleteTransactionRoute> = async (c) => {
  const user = c.get("user");
  const { id } = c.req.valid("param");

  await deleteTransactionAction(id, user.id);
  return c.body(null, StatusCodes.NO_CONTENT);
};
