import type { TransactionResponse, TransactionWithRelations } from "./types";

import { formatISO } from "date-fns";

import { mapAccountResponse } from "~/features/accounts/mappers";
import { mapCategoryResponse } from "~/features/categories/mappers";

export const mapTransactionResponse = (transaction: TransactionWithRelations): TransactionResponse => ({
  id: transaction.id,
  transactionDate: formatISO(transaction.transactionDate, { representation: "date" }),
  categoryId: transaction.categoryId,
  category: transaction.category ? mapCategoryResponse(transaction.category) : undefined,
  type: transaction.type,
  description: transaction.description,
  accountId: transaction.accountId,
  account: mapAccountResponse(transaction.account),
  amount: transaction.amount,
  accountToId: transaction.accountToId,
  accountTo: transaction.accountTo ? mapAccountResponse(transaction.accountTo) : undefined,
  amountTo: transaction.amountTo,
  baseAmount: transaction.baseAmount,
  baseAmountTo: transaction.baseAmountTo,
  createdAt: transaction.createdAt.toISOString(),
  updatedAt: transaction.updatedAt.toISOString(),
});
