import type { Transaction, TransactionResponse } from "./types";

export const mapTransactionResponse = (transaction: Transaction): TransactionResponse => ({
  id: transaction.id,
  userId: transaction.userId,
  transactionDate: transaction.transactionDate,
  categoryId: transaction.categoryId,
  type: transaction.type,
  description: transaction.description,
  accountId: transaction.accountId,
  amount: transaction.amount,
  accountToId: transaction.accountToId,
  amountTo: transaction.amountTo,
  baseAmount: transaction.baseAmount,
  baseAmountTo: transaction.baseAmountTo,
  createdAt: transaction.createdAt.toISOString(),
  updatedAt: transaction.updatedAt.toISOString(),
});
